import { currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache';
import { checkCreditBalance } from "@/lib/credit-balance";
import { newThread } from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
} from "@/lib/databaseUtils"; 
import { tokenCounter } from "@/lib/token-counter";
import { toUIMessageStream } from '@ai-sdk/langchain';
import { createUIMessageStreamResponse, UIMessage } from 'ai';
import { StreamMode } from "@/components/langgraph/Settings";
import { ThreadState } from "@/components/langgraph/Schema";
import { createGraph } from "@/ai/sqlAgent/index";
import { createMemorableEventConfig } from "@/ai/memory_service/utils";
import { userProfileTool, threadSummaryTool } from "@/ai/memory_service/tools"
import { INTERVIEW_PROMPT_V2 } from "@/ai/sqlAgent/prompts"
import { SYSTEM_PROMPT as MEMORY_PROMPT } from "@/ai/memory_service/tools";

//export const runtime = "edge";
//export const preferredRegion = ['hnd1']

export async function POST(req: Request, props: { params: Promise<{ interviewId: string }> }) {
  const params = await props.params;
  const {
    companion,
    threadId,
    existingThread,
    messages,
    chatRuns,
    observationList,
    useReflectionThreshold,
    persona,
    aiLanguage,
    path,
    targetCompanionId,
    targetThreadId,
    targetUserId,
  } = await req.json();

  const user = await currentUser();
  const userId = user?.id!
  const companionId = companion?.id!
  //console.log("targetThreadId: ", targetThreadId)
  //console.log("targetUserId: ", targetUserId)

  if (!userId) {
    return new NextResponse('Unauthorized', { status: 401 })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  //const freeTrial = await checkApiLimit();
  const creditAccess = await checkCreditBalance();
  //if (!freeTrial && !creditAccess) {
  if (!creditAccess) {
    return new NextResponse(
      JSON.stringify({ 
        error: "Your credit is empty. Please purchase more credits." 
      }), 
      { 
        status: 403,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }

  //console.log("observationList", observationList)

  try {
    const maxMessages = 17
    let numberOfMessagesToSlice = useReflectionThreshold
    numberOfMessagesToSlice = Math.max(numberOfMessagesToSlice, maxMessages);
    const latestMessages = messages.slice(-numberOfMessagesToSlice);
  
    const latestUserMessage = messages
    .filter((message: { role: string }) => message.role === 'user')
    .pop() //retrieve the last element

    const userPrompt = latestUserMessage?.parts.filter(part => part.type === 'text').map(part => part.text).join(' ')
    console.log("userPrompt", userPrompt)
    let input: Record<string, any> | null = null;
    if (userPrompt !== null) {
      input = {
        messages: 
          {
            role: "user",
            content: userPrompt,
          },
      };
    }
  
    const memorableTools = [...userProfileTool, ...threadSummaryTool];
    const memorySchemas = createMemorableEventConfig(
      memorableTools,
      MEMORY_PROMPT
    );
    const config = {
      recursionLimit: 8,
      configurable: {
        user_id: userId,
        assistant_id: companionId,
        thread_id: threadId,
        target_user_id: targetUserId,
        target_thread_id: targetThreadId,
        target_companion_id: targetCompanionId,
        system_prompt: INTERVIEW_PROMPT_V2,
        language: aiLanguage?.value ?? 'zh-TW',
        schemas: memorySchemas,
        model: "groq/llama-3.3-70b-versatile",
        delay: 0,
      },
      version: "v2" as "v2",
      encoding: undefined, //"text/event-stream",
    };
  
    const graph = await createGraph()
    //console.log("graph: ", graph)
    //const state = await graph.getState(config);
    //console.log("state: ", state)
    const stream = graph.streamEvents(input, config);
    
    return createUIMessageStreamResponse({
      stream: toUIMessageStream(stream),
    });

  } catch (error: any) {
    console.error("Error fetching sqlAgent graph:", error);
    return new NextResponse(`Error: ${error.message}`, { status: 500 });
  }
}