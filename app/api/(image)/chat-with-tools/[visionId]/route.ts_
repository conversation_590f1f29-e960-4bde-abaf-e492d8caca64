//import axios from "axios";
import { kv } from "@vercel/kv";
import { revalidatePath } from 'next/cache'
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import {
  OpenAIStream,
  StreamingTextResponse,
  Tool,
  ToolCallPayload,
  StreamData,
} from "ai";
import prismadb from "@/lib/prismadb";
import { Role } from "@prisma/client";
import { PromptTemplate } from '@langchain/core/prompts'
import { tools, runFunction } from "./functions";
import { 
  getThread,
  newThread, 
} from '@/app/actions/threadActions'
import { 
  saveCompletionToDatabase,
  saveVisionMessageToDatabase,
} from "@/lib/databaseUtils";
import { v4 as uuidv4 } from 'uuid'


export const runtime = "edge";
export const preferredRegion = ['hnd1']

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});


//const TEMPLATE = `ONLY generate plain sentences without prefix of who is speaking. DO NOT use {companionName}: prefix.
//Your name is {companionName}
const TEMPLATE = `
{companionInstructions}
{companionSeed}
`

enum MediaType {
  image = 'image',
  video = 'video',
}

export async function POST(req: Request, props: { params: Promise<{ visionId: string }> }) {
  const params = await props.params;
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
  ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(50, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
          "X-RateLimit-Limit": limit.toString(),
          "X-RateLimit-Remaining": remaining.toString(),
          "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }

  //const formData = await req.formData();
  const {
    chatRuns,
    companion,
    threadId,
    existingThread,
    messages,
    data: imageToVisionData, //rename to avoid conflict.
    function_call } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const path = `/vision/${params.visionId}/${threadId}`
  //const userId = (await currentUser())?.id

  if (!userId) {
    return new NextResponse('Unauthorized', {
      status: 401
    })
  }

  if (!companion) {
    return new NextResponse("Companion not found", { status: 404 });
  }

  // req.json() and req.formData(), use either one, cannot retrieve twice in the req body.
  /*if (formData) {
    try {
      // Access the formData from req.body
      console.log("formData: ", formData)

      const mode = formData.get("mode");
      const image = formData.get("image");
      const mask = formData.get("mask");
      const prompt = formData.get("prompt");
      const amount = formData.get("n") as string;
      const size = formData.get("size");
      const count = amount !== null ? parseInt(amount, 10) : 1

      console.log("VisionApi: mode =", mode);
      console.log("VisionApi: image =", image);
      console.log("VisionApi: mask =", mask);
      console.log("VisionApi: count =", count);
      console.log("VisionApi: size =", size);

      if (mode === "edit" || mode === "contEdit") {
        // Convert image and mask to base64-encoded strings if they are not null
        let imageBase64: string | null = null;
        let maskBase64: string | null = null;
    
        imageBase64 = image as string;
        maskBase64 = mask as string;
        console.log("image && mask")
    
        if (!imageBase64 || !maskBase64) {
          // Handle the case where image or mask is null
          return new NextResponse("Image or mask is missing.", { status: 400 });
        }
      
        
        const response = await openai.images.edit({
            image: imageBase64 as any,
            mask: maskBase64 as any,
            prompt: prompt as any,
            n: 1,
            size: "512x512",
        })
        console.log("response.data: ", response.data)
        return NextResponse.json(response.data);
    
      } else if (mode === "generate") {
        // Mark out if in test
        const response = await openai.images.generate({
            prompt: prompt.content as any,
            n: 1,
            size: "512x512",
        });
        //
    
        console.log("text to image")
    
        // For Test
        interface MyResponse {
          data: { url: string }[]; 
        }
    
        const response: MyResponse = {
          data: [
            {
              url: 'https://res.cloudinary.com/dyuei3zjr/image/upload/v1697603962/qnyru0csohqmibirtlqb.png'
            }
          ]
        }
        console.log("response.data: ", response.data)
        return NextResponse.json(response.data);
      }
      //


    } catch (error) {
      console.error("Error fetching formData:", error);
      return new NextResponse("formData not found", { status: 404 });
    }
  }*/

  /*if (chatRuns <= 1 || !chatRuns) {
    const greetingMessage: string = `很高興見到您。請問今天需要什麼幫助嗎？`
    async () => {
      if (greetingMessage) {
        await saveCompletionToDatabase(
          params.visionId, userId, greetingMessage, "assistant"
        );
      }
    }

    return new NextResponse(greetingMessage)
  }*/

  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    //companionName: companion.name,
    //userName: user.firstName || '',
    companionInstructions: "", //companion.instructions,
    companionSeed: "" //companion.seed
  })

  // Get the last 8 messages
  const latestMessages = messages.slice(-2);

  const combinedMessages = [
    { role: 'system', content: systemPrompt },
    ...latestMessages,
  ]

  const prompt = combinedMessages.slice(-1)
    .filter(message => message.role === 'user')
    .pop() //retrieve the last element

  //const prompt = messages[messages.length - 1];

  console.log("combinedMessages: ", combinedMessages)
  console.log("prompt: ", prompt)


  if (imageToVisionData && imageToVisionData.chatWithVisionImageUrl.length > 0) {  
    // Ask OpenAI for a streaming chat completion given the prompt
    /*const imageWithMessage = prompt.content + '\n' + `![image](${imageToVisionData.chatWithVisionImageUrl})`;
    latestMessages[latestMessages.length - 1] = {
      role: 'user',
      content: imageWithMessage
    };*/

    const response = await openai.chat.completions.create({
      model: 'gpt-4.1',
      stream: true,
      max_tokens: 500,
      messages: [
        ...latestMessages.slice(-1), //NOT sure if need to include the system prompt
        {
          role: 'user',
          content: [
            { type: 'text', text: prompt.content },
  
            // forward the image information to OpenAI:
            {
              type: 'image_url',
              image_url: imageToVisionData.chatWithVisionImageUrl,
            },
          ],
        },
      ],
    });
  
    // Convert the response into a friendly text-stream
    const stream = OpenAIStream(response, {
      onStart: async () => {
        let newPrompt: string = ''
        if (imageToVisionData && imageToVisionData.chatWithVisionImageUrl.length > 0) {
          newPrompt = prompt.content + '\n' + 
          `![image](${imageToVisionData.chatWithVisionImageUrl})`;
          
        } else {
          newPrompt = prompt.content
        }

        if (newPrompt) {
          if (existingThread) {
            await saveCompletionToDatabase(
              params.visionId, userId, newPrompt, "user", threadId
            );
          } else {
            const name = newPrompt.substring(0, 100);
            await newThread({
              threadId, 
              name, 
              companionId: params.visionId, 
              content: newPrompt,
              role: "user", 
            });
          }
        }
      },
      onFinal: async (completion: string) => {
        await saveCompletionToDatabase(
          params.visionId, userId, completion, "assistant", threadId
        );
        revalidatePath(path)
      },
    })

    // Respond with the stream
    return new StreamingTextResponse(stream);
  }



  // check if the conversation requires a function call to be made
  const initialResponse = await openai.chat.completions.create({
      model: "gpt-4.1-mini",
      messages: combinedMessages,
      stream: true,
      tools,
      tool_choice: "auto",
  });

  let imageUrls: string[] = []

  /* FIXME(@ai-sdk-upgrade-v5): The `StreamData` type has been removed. Please manually migrate following https://ai-sdk.dev/docs/migration-guides/migration-guide-5-0#stream-data-removal */
  const data = new StreamData();
  const funcStream = OpenAIStream(initialResponse, {
    experimental_onToolCall: async (
      call: ToolCallPayload,
      appendToolCallMessage,
    ) => {
      for (const toolCall of call.tools) {
        console.log("toolCall: ", toolCall)
        const result = await runFunction(toolCall.func.name, toolCall.func.arguments);
        console.log("result: ", result)
        
        if (result !== null) {
          if ('imageUrls' in result!) {
            const resultImageUrls = (result as { imageUrls: string[] }).imageUrls;
            imageUrls = resultImageUrls;
            console.log("imageUrls: ", imageUrls)
        
            // Construct content array based on the number of imageUrls
            const contentArray = [
              { type: "text", text: "what's in this image? Response in zh-TW" },
                ...imageUrls.map((imageUrl) => ({
                  type: "image_url",
                  image_url: {
                    "url": imageUrl,
                    "detail": "low"
                  },
              })),
            ];

            const newMessages = [
              ...latestMessages,
              {
              role: 'user',
              content: contentArray,
              },
            ];

            console.log("newMessages: ", newMessages)
            return openai.chat.completions.create({
              model: "gpt-4.1",
              stream: true,
              max_tokens: 500,
              messages: newMessages,
            });

            /*const newMessages = createFunctionCallMessages(result);
            console.log("newMessages: ", newMessages)

            return openai.chat.completions.create({
                model: "gpt-4.1-mini",
                stream: true,
                messages: [...latestMessages, ...newMessages ],
            });*/
          }
        }
      }
    },
    onStart: async () => {  
      if (existingThread) {
        await saveCompletionToDatabase(
          params.visionId, userId, prompt.content, "user", threadId
        );
      } else {
        const name = prompt.content.substring(0, 100);
        await newThread({
          threadId, 
          name, 
          companionId: params.visionId, 
          content: prompt.content,
          role: "user", 
        });
      }      
    },
    onCompletion: async (completion: string) => {
      console.log("completion@api/image/vision", completion)
      console.log("imageUrls@completion: ", imageUrls)
      if (imageUrls !== null && imageUrls !== undefined && imageUrls.length > 0) {
        for (const imageUrl of imageUrls) {
          data.append({
            type: 'numOfImage',
            content: '',
            url: '',
            numOfImage: imageUrls.length
          });

          try {
            console.log("imageUrl@onFinal", imageUrl)

            const imageUrlMessage = {
              id: uuidv4(),
              userId,
              role: Role.assistant,
              content: '',
              image: imageUrl,
              companionId: companion.id,
              threadId
            };
          
            const completionMessage = await prismadb.message.create({
              data: imageUrlMessage,
              select: { id: true },
            });

            console.log("imageUrlMessage@route/visionId : " ,imageUrlMessage)
            if (imageUrls !== null && imageUrls !== undefined && imageUrls.length > 0) {
              if (imageUrlMessage) {
                data.append({
                  type: 'image',
                  content: imageUrlMessage.id,
                  url: imageUrl,              
                });
                console.log("imageUrlMessage@vision/route", data)
              }
            }

            console.log("data@route/vision: ", data)

          } catch (error) {
            console.error('Error updating prismadb:', error)
          }
        }
      }
    },
    onFinal: async (completion: string) => {
      const messageData = {
        id: uuidv4(),
        userId,
        role: Role.assistant,
        content: completion,
        companionId: companion.id, 
        threadId
      };
  
      const completionMessage = await prismadb.message.create({
        data: messageData,
        select: { id: true },
      });

      console.log("completionMessage@route/visionId : " ,completionMessage)
      if (imageUrls !== null && imageUrls !== undefined && imageUrls.length > 0) {
        if (completionMessage) {
          data.append({
            type: 'visionId',
            content: completionMessage.id,
            url: "",
          });
          console.log("completionMessage@vision/route", data)
        }
      }
      data.close();
      revalidatePath(path)
    },

    experimental_streamData: true,
  });

  return new StreamingTextResponse(funcStream, {}, data);
}