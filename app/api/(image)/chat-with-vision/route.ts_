
import { createOpenAI } from '@ai-sdk/openai';
import { streamText } from 'ai';

// Create an OpenAI API client (that's edge friendly!)
const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

// IMPORTANT! Set the runtime to edge
//export const runtime = 'edge';

export async function POST(req: Request) {
  // 'data' contains the additional data that you have sent:
  const { messages, data } = await req.json();

  const initialMessages = messages.slice(0, -1);
  const currentMessage = messages[messages.length - 1];

  // Ask OpenAI for a streaming chat completion given the prompt
    const res = streamText({
      model: openai('gpt-4.1'),
      temperature: 0.5,
      maxOutputTokens: 150,
      messages: [
        ...initialMessages,
        {
          ...currentMessage,

          parts: [
            { type: 'text', text: currentMessage.content },

            // forward the image information to OpenAI:
            {
              type: 'image_url',
              image_url: data.imageUrl,
            },
          ]
        },
      ],
    })

  return res.toUIMessageStreamResponse();
;
}