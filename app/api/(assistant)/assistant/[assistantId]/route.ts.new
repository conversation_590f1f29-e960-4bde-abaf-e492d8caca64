import { kv } from "@vercel/kv";
import { Ratelimit } from "@upstash/ratelimit";
import { OpenAI } from "openai";
import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from "next/server";
import { revalidatePath } from 'next/cache'
import { PromptTemplate } from '@langchain/core/prompts'
import {
  OpenAIStream,
  StreamingTextResponse,
  Tool,
  ToolCallPayload,
  AssistantResponse,
  experimental_StreamData,
} from "ai";
import { 
    Companion,
    UserThread,
    Message as chatMessage,
    Observations,
    ChatSummary,
    Persona,
    Todos
} from '@prisma/client'
import { 
    newUserThread, 
  } from '@/app/actions/assistantActions'
import { 
  saveChatSummaryToDatabase,
  saveCompletionToDatabase,
  saveObservationToDatabase
} from "@/lib/databaseUtils";

const TEMPLATE = `
**Provide better responses considering the following information about me:**
My name is {userName}
{companionInstructions}

**This is how I would like you to response:**
Your name is {companionName}
{companionSeed}

Above all, it is imperative that you respond in the same way the ChatGPT Custom Instructions would respond.
`

const OBSERVATION_TEMPLATE: string = `
You act as a psychologist to summarize your observation of my capabilities, personalities, values, motivation, and interest.

You can adopt my discourse particles and use them when responding to me. 
Do not provide suggestions. In each response, use less than 100 words. Be warm and positive. Not repeat what I've said. 

Respond in Traditional Chinese.
Use emojis only when my mood is positive.
`

const SUMMARIZE_END_TEMPLATE: string = 
`Summarize Key Actions in a markdown table format` +
` with no more than 50 words in zh-tw, title='<<關鍵行動摘要>>'\n\n` +
` Provide 1 Affirmative Sentences in 20 words, title='<<鼓勵的話>>'\n`

const CHAT_SUMMARIZE_TEMPLATE = `
Summarize user key background info in 50 words from previous conversations. 
Do not repeat what I've said. Be concise, response in zh-TW.
`

// Create an OpenAI API client (that's edge friendly!)
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

// IMPORTANT! Set the runtime to edge
export const runtime = 'edge';

const homeTemperatures = {
  bedroom: 20,
  'home office': 21,
  'living room': 21,
  kitchen: 22,
  bathroom: 23,
};

export async function POST(
  req: Request,
  { params }: { params: { assistantId: string } }
  ) {
  if (    
    process.env.KV_REST_API_URL &&
    process.env.KV_REST_API_TOKEN
 ) {
    const ip = req.headers.get("x-forwarded-for");
    const ratelimit = new Ratelimit({
      redis: kv,
      limiter: Ratelimit.slidingWindow(550, "1 d"),
    });

    const { success, limit, reset, remaining } = await ratelimit.limit(
      `companionAI_ratelimit_${ip}`,
    );

    if (!success) {
      return new NextResponse("You have reached your request limit for the day.", {
        status: 429,
        headers: {
        "X-RateLimit-Limit": limit.toString(),
        "X-RateLimit-Remaining": remaining.toString(),
        "X-RateLimit-Reset": reset.toString(),
        },
      });
    }
  }
    
  // Parse the request body
  const input: {
    companion: Companion;
    threadId: string | null;
    message: string
    persona: Persona
    existingThread: UserThread
    userThreadId: string | null
    path: string
  } = await req.json();

  const user = await currentUser()
  const userId = user?.id
  const companionId = input.companion?.id!
  if (!userId) {
    return new NextResponse('Unauthorized', {
        status: 401
      })
  }

  const assistantId = input.companion?.assistantId!
  const systemPromptTemplate = PromptTemplate.fromTemplate(TEMPLATE);
  const systemPrompt = await systemPromptTemplate.format({
    companionName: input.companion.name,
    userName: input.persona?.nickName ?? user.firstName,
    companionInstructions: input.companion.instructions,
    companionSeed: input.companion.seed,
  })

  // Create a thread if needed
  let threadId: string;
  if (input.existingThread?.threadId) {
    threadId = input.existingThread.threadId
  } else if (input?.threadId) {
    threadId = input.threadId
  } else {
    const createdThread = await openai.beta.threads.create({});
    threadId = createdThread.id;
  }
  

  // Add a message to the thread
  const createdMessage = await openai.beta.threads.messages.create(threadId, {
    role: 'user',
    content: input.message,
  });

  if (input.threadId || input.existingThread || input.userThreadId) {
    /*await saveCompletionToDatabase(
      params.assistantId, userId, input.message, "user", threadId
    );*/
    console.log("existingThread")
  } else {
    console.log("newThreadId from openAI: ", threadId)
    const name = input.message.substring(0, 100);
    await newUserThread({
        threadId, 
        name, 
        companionId: params.assistantId, 
        path: `${input.path}`,
    });
  }

  return AssistantResponse(
    { threadId, messageId: createdMessage.id },
    async ({ forwardStream, sendDataMessage }) => {
      // Run the assistant on the thread
      const runStream = openai.beta.threads.runs.createAndStream(threadId, {
        assistant_id:
          assistantId ??
          (() => {
            throw new Error('ASSISTANT_ID is not set');
          })(),
          model: "gpt-4.1-mini",
          //instructions: OBSERVATION_TEMPLATE,
          //additional_instructions: null,
      });

      // forward run status would stream message deltas
      let runResult = await forwardStream(runStream);

      // status can be: queued, in_progress, requires_action, cancelling, cancelled, failed, completed, or expired
      while (
        runResult?.status === 'requires_action' &&
        runResult.required_action?.type === 'submit_tool_outputs'
      ) {
        const tool_outputs =
          runResult.required_action.submit_tool_outputs.tool_calls.map(
            (toolCall: any) => {
              const parameters = JSON.parse(toolCall.function.arguments);
 
              switch (toolCall.function.name) {
                case 'getRoomTemperature': {
                  const temperature =
                    homeTemperatures[
                      parameters.room as keyof typeof homeTemperatures
                    ];
 
                  return {
                    tool_call_id: toolCall.id,
                    output: temperature.toString(),
                  };
                }
 
                case 'setRoomTemperature': {
                  const oldTemperature =
                    homeTemperatures[
                      parameters.room as keyof typeof homeTemperatures
                    ];
 
                  homeTemperatures[
                    parameters.room as keyof typeof homeTemperatures
                  ] = parameters.temperature;
 
                  sendDataMessage({
                    role: 'data',
                    data: {
                      oldTemperature,
                      newTemperature: parameters.temperature,
                      description: `Temperature in ${parameters.room} changed from ${oldTemperature} to ${parameters.temperature}`,
                    },
                  });
 
                  return {
                    tool_call_id: toolCall.id,
                    output: `temperature set successfully`,
                  };
                }
 
                default:
                  throw new Error(
                    `Unknown tool call function: ${toolCall.function.name}`,
                  );
              }
            },
          );
 
        runResult = await forwardStream(
          openai.beta.threads.runs.submitToolOutputsStream(
            threadId,
            runResult.id,
            { tool_outputs },
          ),
        );
      }
    },
  );
}