"use client";

import axios from 'axios'
import DOMPurify from 'dompurify'
import { use<PERSON>tom } from "jotai";
import { 
  getSelectedOrg<PERSON>tom, 
  getSelected<PERSON>ser<PERSON>tom, 
  getSelectedThread<PERSON>tom,
  getSelected<PERSON>ersona<PERSON>tom,
  getSelectedCompanion<PERSON>tom,
} from '@/atoms';
import { type FormEvent, useRef, useEffect, useState, useTransition } from "react";
import { DefaultChatTransport } from 'ai';
import {
  useChat,
  useCompletion,
  type UIMessage,
} from '@ai-sdk/react';
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from 'uuid'
import { useSessionStorage } from 'usehooks-ts'
import { deleteMessageAction, saveObservationAction } from "@/app/actions";
import { addTodoAction } from "@/app/actions/todoActions"
import { addObservation } from "@/app/actions/getObservations"
import { newThread } from '@/app/actions/threadActions'
import { saveCompletionToDatabase } from "@/lib/databaseUtils"; 
import { 
  Companion,
  Message as chatMessage,
  Observations,
  ChatSummary,
  Persona,
  Todos,
  Note,
} from '@prisma/client'
import { SupabaseFile } from '@/lib/types'
import { 
  type OrganizationView, 
} from "@/app/actions/getOrganizations";
import { 
  SentimentData,
  PolarAreaChartType,
  QuestionWithOptions
} from '@/lib/post.types'
import { Button, buttonVariants } from '@/components/ui/button'
import va from "@vercel/analytics";
import { toast } from 'sonner'
import { usePayPerUseModal } from "@/hooks/use-pay-per-use-modal";
import { ChatHeader } from "@/components/chat-header";
import { ChatList } from '@/components/chat-list'
import { TalkForm } from '@/components/talk/talk-form'
import { AdminInterview } from "@/components/interview/admin-interview-panel"
import { ChatScrollAnchor } from '@/lib/hooks/chat-scroll-anchor'
import { markdownToList } from '@/lib/markdownToList-parser'
import { useLocalStorage } from 'usehooks-ts'
import { type Language, DEFAULT_LANGUAGE } from "@/components/ui/ai-language"
import { Trash2 } from "lucide-react";


type ThreadInfo = { id: string; name: string } | null;

type InterviewClientProps = {
  organizations?: OrganizationView[]
  companion: Companion & {
    messages: chatMessage[]
    observations: Observations[]
    chatSummaries: ChatSummary[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
  threadId: string
  existingThread: ThreadInfo
  notes: Note[]
  supabaseFiles: SupabaseFile[]
  userId: string
  path: string
};


const examples = [
  "",
  "",
  "",
];

export function Chat({
  organizations,
  companion,
  foundPersona,
  threadId,
  existingThread,
  notes,
  path,
  supabaseFiles,
  userId,
}: InterviewClientProps) {
  const router = useRouter();
  const payPerUseModal = usePayPerUseModal();
  const [isMounted, setIsMounted] = useState(false);
  const [observations, setObservations] = useState<Observations[]>(companion ? companion.observations : []);
  const formRef = useRef<HTMLFormElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [_, setNewChatId] = useLocalStorage('newChatId', threadId)

  useEffect(() => {
    if (threadId) {
      setNewChatId(threadId)
    }
  }, [threadId, setNewChatId])


  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [chatRuns, setChatRunCount] = useState<number>(1)
  const [value, setValue] = useSessionStorage('test-key', 1)
  const [totalWordCount, setTotalWordCount] = useState<number>(0)
  const [observationList, setObservationList] = useState<string[]>([]);
  const [chatSummary, setChatSummary] = useState(companion.chatSummaries);
  const [chatSummaryList, setChatSummaryList] = useState<string[]>([]);
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(8)
  const latestThreadId = useRef<string | null>(existingThread ? existingThread.id : null)
  const latestCompletion = useRef<string | null>("getUserChatSummary")
  const [selectedLanguage, setSelectedLanguage] = useLocalStorage<Language | null>('selectedLanguage', DEFAULT_LANGUAGE);
  const newChatPath = `/interview/${companion.id}`

  const [myOrganizations, setMyOrganizations] = useState<OrganizationView[]>(organizations);
  const [selectedOrg, setSelectedOrg] = useAtom(getSelectedOrgAtom(userId));
  const [selectedUser, setSelectedUser] = useAtom(getSelectedUserAtom(userId));
  const [selectedThread, setSelectedThread] = useAtom(getSelectedThreadAtom(userId))
  const [selectedPersona, setSelectedPersona] = useAtom(getSelectedPersonaAtom(userId))
  const [selectedCompanion, setSelectedCompanion] = useAtom(getSelectedCompanionAtom(userId))

  useEffect(() => {
    console.log("selectedUser: ", selectedUser)
    console.log("selectedOrg: ", selectedOrg)
    console.log("selectedThread: ", selectedThread)
    console.log("selectedPersona: ", selectedPersona)
    console.log("selectedCompanion: ", selectedCompanion)
  }, [selectedUser, selectedOrg, selectedThread, selectedPersona, selectedCompanion])
  /*
  useEffect(() => {
    if (companion.messages.length === 0) {
      // Trigger the 'append' function with the "Hello" message when the page loads
      const helloMessage: Message = {
        id: companion.id,
        content: `您好，我是${companion.name}`,
        role: 'system',
      };

      append(helloMessage)
    }
  }, []);
  */

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    setChatSummary(companion.chatSummaries)
    console.log("companion.chatSummaries:", companion.chatSummaries);
  }, [companion.chatSummaries])

  useEffect(() => {
    if (chatSummary.length > 0 && chatRuns <= 1) {
      const latestChatSummaries = companion.chatSummaries
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(latestChatSummaries);
    } else {
      const latestChatSummary = companion.chatSummaries.slice(-1)
        .map(chatSummary => chatSummary.content);
      setChatSummaryList(prevChatSummaryList => [...prevChatSummaryList, ...latestChatSummary]);
    }
  }, [chatSummary, chatRuns]);

  useEffect(() => {
    console.log("companion.observations:", companion.observations);
    setObservations(companion.observations)
  }, [companion.observations])


  const incrementChatRunCount = () => {
    setChatRunCount((prevCount) => prevCount + 1);
    setValue((prevValue) => prevValue + 1);
  };

  const incrementWordCount = (count: number) => {
    setTotalWordCount((prevCount) => prevCount + count);
  };

  const {
    complete, 
    completion
  } = useCompletion({
    /*api: `/api/observation/${companion.id}`,
    body: {      
      companion,
      observationList,
      useReflectionThreshold,
      persona: {
        name: usePersona?.name,
        nickName: usePersona?.nickName,
        age: usePersona?.age,
        traits: usePersona?.traits,
        status: usePersona?.status,
      },
      path
    },*/
    api: `/api/summary/${companion.id}/google`,
    body: {
      aiLanguage: selectedLanguage,
      observationList,
      companionName: companion.name,
      userName: usePersona?.nickName,
      threadId,
      chatRuns,
      useReflectionThreshold,
      persona: {
        name: usePersona?.name!,
        nickName: usePersona?.nickName!,
        age: usePersona?.age!,
        traits: usePersona?.traits!,
        status: usePersona?.status!,
      },
      latestCompletion,
      path,
    },
    /*onResponse: res => {
      if (res.status === 429) {
        toast.error("You have reached your request limit for the day.");
      }
      if (res.status === 403) {
        payPerUseModal.onOpen();
      } else {
        toast.error("Something went wrong.");
      }
    },*/
    onFinish(_prompt, completion) {
      //console.log("completion: ", completion);
      if (userId && completion && latestCompletion.current === "getObservation") {
        const newObservation = {
          id: uuidv4(),
          title: completion,
          message: completion,
          roomId: 'user_status_sum',
          createdAt: new Date(),
          updatedAt: new Date(),
          companionId: companion.id!,
          userId: userId!,
        };
        setObservations((prevObservations) => [newObservation, ...prevObservations]);
        setTotalWordCount(0)
        latestCompletion.current = "getUserChatSummary"
      }
      if (userId && completion && latestCompletion.current === "getUserChatSummary") {
        const newChatSummary = {
          id: uuidv4(),
          title: completion,
          content: completion,
          createdAt: new Date(),
          companionId: companion.id!,
          companionName: companion.name!,
          userId: userId,
          userName: usePersona?.nickName!,
          threadId,
        };
        setChatSummary((prevChatSummary) => [newChatSummary, ...prevChatSummary]);
        latestCompletion.current = "getObservation"
      }
    },
  });

  const [input, setInput] = useState('');
  const latestUserInput = useRef<string | null>(null);

  const {
    messages,
    sendMessage,
    status
  } = useChat({
    //api: `/api/crag/groq`,
    //streamMode: 'text', // enable it only when use raw text stream
    messages: companion.messages.map((message) => ({
      id: message.id,
      parts: [{
        type: 'text',
        text: message.content,
      }],
      role: message.role,
    })),
    transport: new DefaultChatTransport({
      api: `/api/interview/${companion.id}/graph`,
      body: () => ({
        companion,
        threadId,
        existingThread: latestThreadId.current,
        totalWordCount,
        chatSummaryList,
        observationList,
        useReflectionThreshold,
        chatRuns,
        ...(selectedPersona && { // Only include if selectedPersona is available
          persona: {
            name: selectedPersona.name,
            nickName: selectedPersona.nickName,
            age: selectedPersona.age,
            traits: selectedPersona.traits,
            status: selectedPersona.status,
          }
        }),
        aiLanguage: selectedLanguage,
        path,
        targetCompanionId: selectedCompanion?.id!,
        targetThreadId: selectedThread?.id!,
        targetUserId: selectedUser?.id!,
      })
    }),
    /*onResponse: (response) => {
      if (response.status === 403) {
        payPerUseModal.onOpen();
      }
      if (response.status === 429) {
        toast.error("You have reached your request limit for the day.");
        va.track("Rate limited");
        return;
      } else {
        va.track("Chat initiated");
      }
    },*/

    onError: (error) => {
      //toast.error(error.message)
    },

    onFinish: async ({ message }) => {
      const promises: Promise<any>[] = [];
      console.log("message: ", message)
      console.log("messages: ", messages)

      // Check if we need to run the sentiment analysis
      /*const sentimentAnalysisPromise = (chatRuns % (useReflectionThreshold) === 100) ? (async () => {
        try {
          const userMessages = messages.filter((message) => message.role === "user");
          const sentiment: { data: SentimentData } = await axios.post('/api/sentiment', {
            userMessages,
            useReflectionThreshold,
          });
    
          const sentimentResult = sentiment.data;
          console.log("sentimentResult: ", sentimentResult);
    
          const newData: SentimentData = {
            emotions: labels.map((emotion) => {
              const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion);
              return emotionIndex !== -1 ? emotion : emotion;
            }),
            stars: labels.map((emotion) => {
              const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(emotion);
              return emotionIndex !== -1 ? (sentimentResult?.stars ?? [])[emotionIndex] : 0;
            }),
          };
    
          // Update emotions data for chart
          const newEmotionsData: PolarAreaChartType = updatePolarAreaChart(newData);
          await setEmotionsData(newEmotionsData);
        } catch (error) {
          toast.error("Something went wrong.");
          console.error("Error in sentiment analysis:", error);
        }
      })() : Promise.resolve(); // Skip if the condition is not met
      */

      // Initiate addObservation task and let it run in the background.
      /*const observationSummaryPromise = addObservation({
        threadId,
        companionId: companion.id!,
        prompt: input,
        useReflectionThreshold,
        persona: {
          name: usePersona?.name!,
          nickName: usePersona?.nickName!,
          age: usePersona?.age!,
          traits: usePersona?.traits!,
          status: usePersona?.status!,
        },
        path,
      });*/

      //promises.push(sentimentAnalysisPromise, observationSummaryPromise);
    
      if (message && message?.parts && message?.role === "assistant") {
        // Count the number of words in latestUserMessage.content
        const words: string = message.parts
          .filter(part => part.type === 'text')
          .map(part => (part as { type: 'text'; text: string }).text)
          .join('\n');
        console.log("words", words);
      
        // Count the number of words in latestUserMessage.content
        incrementWordCount(words.length);
        
        // Update the wordList state
        setObservationList(prevObservationList => [...prevObservationList, words]);
      
        const currentUserInput = latestUserInput.current;

        if (!latestThreadId?.current && currentUserInput) {
          // Create a new thread and save the user message as part of thread creation
          const title = currentUserInput.substring(0, 10);
          const username = selectedPersona?.email?.split('@')[0]; // No default
          const nameParts = [selectedPersona?.name, username ? `(${username})` : null, title].filter(Boolean);
          const name = nameParts.join(" ");
          console.log("!latestThreadId.current && input")
          

          await newThread({
            threadId,
            name,
            companionId: companion?.id!,
            content: currentUserInput,
            role: "user",
          });
        } else if (currentUserInput) {
          // Save the user message only if the thread already exists
          console.log("same threadId");
          await saveCompletionToDatabase(
            companion?.id!,
            userId,
            currentUserInput,
            "user",
            threadId
          );
        }
        latestUserInput.current = null;
  
        promises.push(
          saveCompletionToDatabase(
            companion?.id!,
            userId,
            message.parts
              .filter(part => part.type === 'text')
              .map(part => (part as { type: 'text'; text: string }).text)
              .join('\n'),
            "assistant",
            threadId
          )
        );
      }
    
      //console.log("totalWordCount", totalWordCount);
    
      // Run the summarization logic based on certain conditions
      if (chatRuns % (useReflectionThreshold * 1.5) === 0 && totalWordCount >= 50) {
        latestCompletion.current = "getObservation";
        //console.log("getObservation");
        complete("Summarize my capabilities, personalities, values, motivation, and interest.");
      } else if (chatRuns % (useReflectionThreshold) === 0) {
        latestCompletion.current = "getUserChatSummary";
        //console.log("getUserChatSummary");
        complete("Summarize the key messages.");
      }
    
      // Process the user_todos in parallel
      const markdownTable = message.parts
        .filter(part => part.type === 'text')
        .map(part => (part as { type: 'text'; text: string }).text)
        .join('\n');
    
      //console.log("markdownTable@cheers: ", markdownTable);
      const user_todos = markdownToList(markdownTable);
      //console.log("user_todos@cheers: ", user_todos);

      if (user_todos && user_todos.length > 0) {
        promises.push(
          ...user_todos.map((todo) =>
            addTodoAction({
              companionId: companion.id,
              task: todo.action,
              path,
            })
          )
        );
      }
    
      // Await all async tasks to complete
      try {
        await Promise.all(promises);
      } catch (error) {
        toast.error("Something went wrong.");
      }
    
      incrementChatRunCount();
    
      if (threadId && latestThreadId.current !== threadId) {
        const newThreadId = threadId;
        latestThreadId.current = newThreadId;
        console.log("router referesh");
      }
    },
  })

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const currentInput = input; // Capture current input   
    // Store the input in a ref to ensure it's available in onFinish
    latestUserInput.current = currentInput;
    
    sendMessage({ text: currentInput });
    setInput('');
  };

  if (!isMounted) {
    return null;
  }

  const disabled = status !== "ready" && status !== "error" || input.length === 0 || !selectedUser || !selectedThread;
  const isLoading = status !== "ready" && status !== "error";

  return (
    <div className="flex flex-col h-[100vh] w-full items-center lg:px-4 pb-2 space-y-2">
      <ChatHeader companion={companion} />
      <div className="flex-grow w-full pb-[150px] lg:px-4 overflow-y-auto">
        {messages.length ? (
          <>
            <ChatList 
              companion={companion}
              isLoading={isLoading}
              messages={messages}
              path={path}
            />
            <ChatScrollAnchor trackVisibility={isLoading} />
          </>
        ) : (
          <div className="border-gray-200 sm:mx-0 mx-5 mt-2 rounded-md border sm:w-full">
            <div className="flex flex-col space-y-4 p-7 sm:p-10">
              <h1 className="text-lg font-semibold primary-text">
                Welcome to ComfyMinds Lab
              </h1>
              <p className="primary-text">
                {companion.introduction}
              </p>
            </div>
            <div className="flex flex-col space-y-4 border-t border-gray-0 p-1 sm:p-10">
              {examples.map((example, i) => (
                <button
                  key={i}
                  className="rounded-md border border-gray-200 bg-primary/10 px-5 py-1 text-left transition-all duration-75 hover:border-black hover:text-foreground active:bg-gray-50"
                  onClick={() => {
                  setInput(example);
                  inputRef.current?.focus();
                  }}
                >
                  {example}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      {/*<div className="h-[100px]"></div>*/}
      <div className="fixed bottom-0 flex w-full flex-col justify-center items-center">
        <AdminInterview 
          userId={userId}
          organizations={myOrganizations}
        />
        <TalkForm
          handleSubmit={handleSubmit}
          input={input}
          setInput={setInput}
          isLoading={isLoading}
          disabled={disabled}
          path={newChatPath}
          formWidth={'100%'}
        />
      </div>
    </div>
  )
}
