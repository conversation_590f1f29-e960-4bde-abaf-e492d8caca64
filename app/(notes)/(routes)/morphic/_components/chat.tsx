'use client'

import { CHAT_ID } from '@/constants'
import { Model } from '@/ai/morphic/types/models'
import { DefaultChatTransport } from 'ai';
import { type UIMessage, useChat } from '@ai-sdk/react';
//import { useChat } from '@ai-sdk/react'
import { useEffect, useState } from 'react';
import { toast } from 'sonner'
import { ChatMessages } from '@/components/morphic/chat-messages'
import { ChatPanel } from '@/components/morphic/chat-panel'

export function Chat({
  id,
  savedMessages = [],
  query,
  models
}: {
  id: string
  savedMessages?: UIMessage[]
  query?: string
  models?: Model[]
}) {
  const [input, setInput] = useState('');
  const [data, setData] = useState<any | undefined>(undefined)

  const {
    messages,
    status,
    setMessages,
    stop,
    sendMessage,
  } = useChat({
    id: CHAT_ID,
    messages: savedMessages,
    onFinish: () => {
      window.history.replaceState({}, '', `/morphic/search/${id}`)
    },
    onError: error => {
      toast.error(`Error in chat: ${error.message}`)
    },

    transport: new DefaultChatTransport({
      api: `/api/morphic`,
      body: () => ({
        id
      })
    })
  })

  useEffect(() => {
    setMessages(savedMessages)
  }, [id])

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    sendMessage({
      role: 'user',
      parts: [{ type: 'text', text: input }],
    })
    setInput('')
  }

  const isLoading = status !== 'ready' && status !== 'error'

  const onQuerySelect = (query: string) => {
    sendMessage({
      role: 'user',
      parts: [{ type: 'text', text: query }],
    })
  }

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setData(undefined) // reset data to clear tool call
    handleSubmit(e)
  }

  return (
    <div className="flex flex-col h-screen w-full max-w-3xl pt-4 pb-20 mx-auto stretch overflow-auto">
      <ChatMessages
        messages={messages}
        data={data}
        onQuerySelect={onQuerySelect}
        isLoading={isLoading}
        chatId={id}
      />
      <ChatPanel
        input={input}
        handleInputChange={e => setInput(e.target.value)}
        handleSubmit={onSubmit}
        isLoading={isLoading}
        messages={messages}
        setMessages={setMessages}
        stop={stop}
        query={query}
        append={sendMessage}
        models={models}
      />
    </div>
  );
}