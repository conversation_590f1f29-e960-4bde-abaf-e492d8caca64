"use client";

import Textarea from "react-textarea-autosize";
import { UIMessage, DefaultChatTransport } from "ai";
import { useChat } from "@ai-sdk/react";
import { useEffect, useMemo, useState, useRef } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { MemoizedReactMarkdown } from '@/components/markdown'
import { IconSpinner } from '@/components/ui/icons'
import { ArrowUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

type ChatProps = {
  contractId: string;
  filePath: string;
}
export default function Chat({
  contractId,
  filePath
}: ChatProps) {
  const [toolCall, setToolCall] = useState<string>();
  const [input, setInput] = useState('');
  const {
    messages,
    handleSubmit,
    status
  } =
    useChat({
      /* FIXME(@ai-sdk-upgrade-v5): The maxSteps parameter has been removed from useChat. You should now use server-side `stopWhen` conditions for multi-step tool execution control. https://ai-sdk.dev/docs/migration-guides/migration-guide-5-0#maxsteps-removal */
      maxSteps: 4,

      onToolCall({ toolCall }) {
        setToolCall(toolCall.toolName);
      },

      onError: (error) => {
        toast.error(`You've been rate limited, please try again later!, ${error}`);
      },

      transport: new DefaultChatTransport({
        api: `/api/contracts/chat`,
        body: () => ({
          contractId,
          filePath,
        })
      })
    });

  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const messageListRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const isLoading = status !== "ready" && status !== "error";

  useEffect(() => {
    if (messages.length > 0) setIsExpanded(true);
    if (messageListRef.current) {
      requestAnimationFrame(() => {
        messageListRef.current!.scrollTop = messageListRef.current!.scrollHeight;
      });
    }
    console.log("messages===========>", messages)
  }, [messages]);

  const currentToolCall = useMemo(() => {
    const tools = messages?.slice(-1)[0]?.toolInvocations;
    if (tools && toolCall === tools[0].toolName) {
      return tools[0].toolName;
    } else {
      return undefined;
    }
  }, [toolCall, messages]);

  const awaitingResponse = useMemo(() => {
    if (
      isLoading &&
      currentToolCall === undefined &&
      messages.slice(-1)[0].role === "user"
    ) {
      return true;
    } else {
      return false;
    }
  }, [isLoading, currentToolCall, messages]);

  const userQuery: UIMessage | undefined = messages
    .filter((m) => m.role === "user")
    .slice(-1)[0];

  const lastAssistantMessage: UIMessage | undefined = messages
    .filter((m) => m.role !== "user")
    .slice(-1)[0];

  const disabled = input?.length === 0
  return (
    <div className="flex justify-center items-start min-h-screen w-full dark:bg-neutral-900">
      <div className="flex flex-col items-center w-full max-w-[500px]">
        <motion.div
          animate={{
            minHeight: isExpanded ? 200 : 0,
            padding: isExpanded ? 12 : 0,
          }}
          transition={{
            type: "spring",
            bounce: 0.5,
          }}
          className={cn(
            "rounded-lg w-full",
            isExpanded
              ? "bg-neutral-100/0 dark:bg-neutral-800/0"
              : "bg-transparent",
          )}
        >
          <div ref={messageListRef} className="flex flex-col w-full justify-between gap-1 max-h-[calc(100vh-200px)] overflow-y-auto">
            <motion.div
              transition={{
                type: "spring",
              }}
              className="min-h-fit flex flex-col"
            >
              <AnimatePresence>
                {messages.map((message, index) => (
                  <div key={message.id} className="">
                    {message.role === "user" ? (
                      <div className="w-fit max-w-[90%] flex justify-end ml-auto dark:text-neutral-400 text-neutral-600 text-sm mt-4 mb-2 text-right p-2 rounded-2xl rounded-br-none bg-lime-200/20">
                        {message.content}
                      </div>
                    ) : (
                      <AssistantMessage message={message} />
                    )}
                  </div>
                ))}
                
                {(awaitingResponse || currentToolCall) && (
                  <div className="min-h-12">
                    <Loading tool={currentToolCall} />
                  </div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </motion.div>
        <form ref={formRef} onSubmit={handleSubmit} className="absolute bottom-0 w-full sm:max-w-[420px] min-h-[60px] flex items-center bg-gradient-to-r from-indigo-500 via-teak-400 to-indigo-500 px-1 shadow-lg">
          <Textarea
            className={`w-full resize-none p-2 bg-transparent text-white focus-within:outline-none`}
            minLength={3}
            required
            rows={1}
            value={input}
            placeholder={"Ask me anything..."}
            onChange={e => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey && !disabled) {
                formRef.current?.requestSubmit();
                e.preventDefault();
              }
            }}
          />
          <button
            className={cn(
              "absolute right-2 bottom-3 my-auto flex size-8 items-center justify-center rounded-full transition-all",
              disabled
                ? "cursor-not-allowed bg-transparent"
                : "bg-green-600 hover:bg-green-700",
            )}
            disabled={disabled}
          >
            {isLoading ? (
              <IconSpinner />
            ) : (              
              <ArrowUp size={18}
                className={cn(
                  "",
                  disabled ? "text-gray-300/0" : "text-white",
                )}
              />
            )}
            </button>
        </form>        
      </div>
    </div>
  );
}

const AssistantMessage = ({ message }: { message: UIMessage | undefined }) => {
  if (message === undefined) return "HELLO";

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={message.id}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="whitespace-pre-wrap font-mono anti text-sm text-neutral-800 dark:text-neutral-200 overflow-hidden"
        id="markdown"
      >
        <MemoizedReactMarkdown
          className={"max-h-100 overflow-y-scroll no-scrollbar-gutter"}
        >
          {message.content}
        </MemoizedReactMarkdown>
      </motion.div>
    </AnimatePresence>
  );
};

const Loading = ({ tool }: { tool?: string }) => {
  const toolName =
    tool === "getInformation"
      ? "Getting information"
      : tool === "addResource"
        ? "Adding information"
        : "Thinking";

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ type: "spring" }}
        className="overflow-hidden flex justify-start items-center"
      >
        <div className="flex flex-row gap-1 items-center">
          <div className="animate-spin dark:text-neutral-400 text-neutral-500">
            <IconSpinner />
          </div>
          <div className="text-neutral-500 dark:text-neutral-400 text-sm">
            {toolName}...
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
