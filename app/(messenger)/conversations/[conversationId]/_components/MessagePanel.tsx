'use client';

import { HiPhoto } from "react-icons/hi2";
import axios from "axios";
import { DefaultChatTransport } from 'ai';
import { useChat, type UIMessage } from '@ai-sdk/react';
import { CldUploadButton } from "next-cloudinary";
import useConversation from "@/hooks/useConversation";
import { FormEvent, useState } from "react";
import { MessageForm } from './MessageForm';
import { FullMessageType } from "@/lib/types";
import { Persona, Role } from "@prisma/client";
import { Switch } from "@/components/ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Define the type for the extracted message
interface ExtractedMessage {
  id: string;
  role: Role;
  content: string;
}

interface MessagePanelProps {
  messagesBody: FullMessageType[];
  currentUser?: Persona;
  assistant?: Persona;
}

const MessagePanel: React.FC<MessagePanelProps> = ({ messagesBody = [], currentUser, assistant }) => {
  const { conversationId } = useConversation();

  // Initialize extractedData with the correct type
  const extractedData: ExtractedMessage[] = [];

  const [allowJoin, setAllowJoin] = useState<boolean>(false);

  // Loop through the messages in messagesBody
  for (const message of messagesBody) {
    // Create a new object with just the "id", "role", and formatted "content" fields
    const extractedMessage: ExtractedMessage = {
      id: message.id,
      role: message.role,
      content: `${message.sender.name}: ${message.content}`,
    };

    // Push the extracted message into the extractedData array
    extractedData.push(extractedMessage);
  }

  console.log("extractedData@MessagePanel: ", extractedData);

  const [input, setInput] = useState('');

  const {
    messages,
    sendMessage,
    status,
  } = useChat({
    messages: extractedData.map((message) => ({
      id: message.id,
      parts: [{
        type: 'text',
        text: message.content,
      }],
      role: message.role,
    })),
    transport: new DefaultChatTransport({
      api: `/api/messages/${conversationId}`,
      body: () => ({
        allowJoin,
        companionId: assistant?.id!,
        companionUserId: assistant?.userId,
        companionName: assistant?.name,
      })
    }),
    onFinish: async (messages) => {
      setInput("");
    },
  });

  const isLoading = status !== "ready" && status !== "error";
  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    sendMessage({ text: input });
    setInput('');
  };

  const handleUpload = (result: any) => {
    axios.post(`/api/messages/${conversationId}`, {
      image: result.info.secure_url,
    });
  };

  return (
    <div
      className="
        py-4 
        px-4 
        bg-white 
        border-t-[0px]
        flex 
        items-center 
        gap-2 
        lg:gap-4 
        w-full
      "
    >
      {assistant?.name && (
        <div className="flex items-center mt-1 ml-3 w-[3rem]">
          <Tooltip>
            <TooltipTrigger asChild>
              <Switch
                className="flex items-center bg-primary/30 space-x-2"
                id="allowJoin"
                checked={allowJoin}
                onCheckedChange={(e) => setAllowJoin(e)}
              />
            </TooltipTrigger>
            <TooltipContent>
              <p>邀請{assistant.name}加入討論</p>
            </TooltipContent>
          </Tooltip>
        </div>
      )}
      <CldUploadButton
        options={{ maxFiles: 1 }}
        onUpload={handleUpload}
        uploadPreset="wfoehym3"
      >
        <HiPhoto size={30} className="text-sky-500" />
      </CldUploadButton>
      <MessageForm
        isLoading={isLoading}
        input={input}
        handleInputChange={e => setInput(e.target.value)}
        onSubmit={onSubmit}
      />
    </div>
  );
};

export default MessagePanel;
