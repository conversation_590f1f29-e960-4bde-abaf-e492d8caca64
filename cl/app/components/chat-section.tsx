"use client";;
import { useChat, DefaultChatTransport } from "@ai-sdk/react";
import { useState } from 'react';
import { ChatInput, ChatMessages } from "./ui/chat";
import { useClientConfig } from "./ui/chat/hooks/use-config";

export default function ChatSection() {
  const { chatAPI } = useClientConfig();
  const [input, setInput] = useState('');
  const {
    messages,
    isLoading,
    handleSubmit,
    reload,
    stop,
    append,
    setInput
  } = useChat({
    headers: {
      "Content-Type": "application/json", // using JSON because of vercel/ai 2.2.26
    },

    onError: (error: unknown) => {
      if (!(error instanceof Error)) throw error;
      const message = JSON.parse(error.message);
      alert(message.detail);
    },

    transport: new DefaultChatTransport({
      api: chatAPI
    })
  });

  return (
    <div className="space-y-4 w-full h-full flex flex-col">
      <ChatMessages
        messages={messages}
        isLoading={isLoading}
        reload={reload}
        stop={stop}
        append={append}
      />
      <ChatInput
        input={input}
        handleSubmit={handleSubmit}
        e => setInput(e.target.value)={e => setInput(e.target.value)}
        isLoading={isLoading}
        messages={messages}
        append={append}
        setInput={setInput}
      />
    </div>
  );
}
