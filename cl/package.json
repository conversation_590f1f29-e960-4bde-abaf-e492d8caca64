{"name": "cl", "version": "0.1.0", "scripts": {"format": "prettier --ignore-unknown --cache --check .", "format:write": "prettier --ignore-unknown --write .", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate": "tsx app/api/chat/engine/generate.ts"}, "dependencies": {"@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-slot": "^1.0.2", "ai": "^3.0.21", "ajv": "^8.12.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^16.3.1", "llamaindex": "0.3.16", "lucide-react": "^0.294.0", "next": "^14.0.3", "pdf2json": "3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "remark": "^14.0.3", "remark-code-import": "^1.2.0", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "rehype-katex": "^7.0.0", "supports-color": "^8.1.1", "tailwind-merge": "^2.1.0", "vaul": "^0.9.1", "@llamaindex/pdf-viewer": "^1.1.1", "@e2b/code-interpreter": "^0.0.5", "uuid": "^9.0.1", "got": "10.7.0", "@apidevtools/swagger-parser": "^10.1.0"}, "devDependencies": {"@types/node": "^20.10.3", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.11", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^8.10.0", "postcss": "^8.4.32", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "tailwindcss": "^3.3.6", "tsx": "^4.7.2", "typescript": "^5.3.2", "@types/uuid": "^9.0.8"}}