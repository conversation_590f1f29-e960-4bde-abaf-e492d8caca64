import * as React from 'react'
import Link from 'next/link'

import { cn } from '@/lib/utils'
import { UserButton } from "@clerk/nextjs";
import { auth } from "@clerk/nextjs/server";
import { clearUserThreads } from '@/app/actions/assistantActions'
import { But<PERSON>, buttonVariants } from '@/components/ui/button'
import { SidebarSheet } from '@/components/chat/sidebar-sheet'
import { SidebarList } from './sidebar-list'
import {
  IconGitHub,
  IconNextChat,
  IconSeparator,
  IconVercel
} from '@/components/ui/icons'
import { SidebarFooter } from '@/components/chat/sidebar-footer'
import { ClearHistory } from './clear-history'
import ThemeChanger from "@/components/nextly/DarkSwitch";


export interface SidebarHeaderProps {
  companionId: string
  path: string
}

export async function SidebarHeader({
  companionId,
  path
}: SidebarHeaderProps) {
  const { userId } = await auth();
  return (
    <header className="fixed top-0 right-[10%] lg:left-[50%] translate-x-[-18%] flex justify-center items-center w-[2rem] h-11 bg-transparent"
      style={{ zIndex: 53 }}
    >
      <div className="flex items-center">
        {userId && companionId ? (
          <SidebarSheet>
            <React.Suspense fallback={<div className="flex-1 overflow-auto" />}>
              {/* @ts-ignore */}
              <SidebarList userId={userId} companionId={companionId} path={path} />
            </React.Suspense>
            <SidebarFooter>
              <ThemeChanger />
              <ClearHistory clearUserThreads={clearUserThreads} companionId={companionId} path={path} />
            </SidebarFooter>
          </SidebarSheet>
        ) : (
          null
        )}
        {/*
        <div className="flex items-center">
          <IconSeparator className="w-6 h-6 text-muted-foreground/50" />
          <UserButton afterSignOutUrl="/sign-in" />
        </div>
        */}
      </div>
    </header>
  )
}