'use client'

import React, { useEffect, useState, useTransition } from 'react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'

import { UserThread } from "@prisma/client";
import { ServerActionResult } from '@/lib/types'
import { cn, formatDate } from '@/lib/utils'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog'
import { Input } from "@/components/ui/input";
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  IconShare,
  IconSpinner,
  IconTrash,
  IconUsers
} from '@/components/ui/icons'
import { HiMiniPencil } from 'react-icons/hi2';
import Link from 'next/link'
import { badgeVariants } from '@/components/ui/badge'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'

interface SidebarActionsProps {
  thread: UserThread
  renameUserThread: (args: { threadId: string; threadName: string; path: string }) => ServerActionResult<void>
  removeUserThread: (args: { threadId: string; companionId: string; path: string }) => ServerActionResult<void>
  path: string
}

export function SidebarActions({
  thread,
  renameUserThread,
  removeUserThread,
  path
}: SidebarActionsProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [renameDialogOpen, setRenameDialogOpen] = useState(false)
  const [isRemovePending, startRemoveTransition] = useTransition()
  const [isRenamePending, startRenameTransition] = useTransition()
  const [renameThreadName, setRenameThreadName] = useState(thread.name);
  const router = useRouter()

  const handleRenameThread = () => {
    startRenameTransition(async () => {
      try {
        // Optimistically update local state with new thread name
        const oldThreadName = renameThreadName;
        setRenameThreadName(renameThreadName);

        const result = await renameUserThread({
          threadId: thread.threadId,
          threadName: renameThreadName,
          path: path
        });

        setRenameDialogOpen(false)

        if (result && 'error' in result) {
          // Revert local state if the server operation fails
          setRenameThreadName(oldThreadName);
          toast.error(result.error);
        } else {
          toast.success('Chat title renamed');
        }
      } catch (error) {
        console.error("[THREAD_NAME_UPDATED]", error);
        toast.error('Failed to rename chat title');
      }
    });
  };

  return (
    <>
      <div className="flex space-x-1">
        <Popover modal={false} open={renameDialogOpen} onOpenChange={setRenameDialogOpen} >
          <PopoverTrigger asChild>
            {/* Rename button */}
            <Button
              variant="ghost"
              className="h-6 w-6 p-0 hover:bg-background"
              disabled={isRenamePending}
              onClick={() => setRenameDialogOpen(true)}
            >
              <HiMiniPencil />
              <span className="sr-only">Rename</span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="sm:max-w-[425px]" style={{ background: 'var(--gradient)' }}>
            <div className="space-y-12">
              <div className="border-b border-gray-900/10">
                <h2 
                  className="text-base font-semibold leading-7"
                >
                  Chat Title
                </h2>
                <p className="mt-1 text-sm leading-6">
                  Edit this chat title.
                </p>
              </div>
            </div>
            <div className="grid gap-4 py-3">
              <div className="flex flex-col gap-y-1">
                <Input
                  value={renameThreadName}
                  onChange={e => setRenameThreadName(e.target.value)}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-1">
              <Button 
                type="submit"
                disabled={isRenamePending}
                onClick={handleRenameThread}
              >
                {isRenamePending && <IconSpinner className="mr-2 animate-spin" />}
                Save changes
              </Button>
            </div>
          </PopoverContent>
        </Popover>
        {/* Remove button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              className="h-6 w-6 p-0 hover:bg-background"
              disabled={isRemovePending}
              onClick={() => setDeleteDialogOpen(true)}
            >
              <IconTrash />
              <span className="sr-only">Delete</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>Delete chat</TooltipContent>
        </Tooltip>
      </div>
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete your chat message and remove your
              data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isRemovePending}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              disabled={isRemovePending}
              onClick={event => {
                event.preventDefault()
                startRemoveTransition(async () => {
                  const result = await removeUserThread({
                    threadId: thread.threadId,
                    companionId: thread.companionId,
                    path: path
                    //path: chat.path
                  })

                  if (result && 'error' in result) {
                    toast.error(result.error)
                    return
                  }

                  setDeleteDialogOpen(false)                  
                  router.push(path)
                  //router.back()
                  router.refresh()
                  toast.success('Chat deleted')
                })
              }}
            >
              {isRemovePending && <IconSpinner className="mr-2 animate-spin" />}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}