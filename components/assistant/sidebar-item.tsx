'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'

import { UserThread } from "@prisma/client";
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import { IconUsers } from '@/components/ui/icons'
import { MessagesSquare } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'

interface SidebarItemProps {
  thread: UserThread
  path: string
  children: React.ReactNode
}

export function SidebarItem({ thread, path, children }: SidebarItemProps) {
  const pathname = usePathname()
  //const isActive = pathname === path
  const sharePath = '' //`/share/${thread.id}`
  const [isActive, setIsActive] = useState(false);

  if (!thread?.threadId) return null

  return (
    <div
      className="relative"
      onMouseOver={() => { setIsActive(true) }}
      onMouseOut={() => { setIsActive(false) }}
    >
      <div className="absolute left-2 top-2 flex h-6 w-6 items-center justify-center">
        {sharePath.length > 0 ? (
          <Tooltip delayDuration={1000}>
            <TooltipTrigger
              tabIndex={-1}
              className="focus:bg-muted focus:ring-1 focus:ring-ring"
            >
              <IconUsers className="mr-2" />
            </TooltipTrigger>
            <TooltipContent>This is a shared chat.</TooltipContent>
          </Tooltip>
        ) : (
          <MessagesSquare size={"16"} />
        )}
      </div>
      <Link
        href={path}
        className={cn(
          buttonVariants({ variant: 'ghost' }),
          'group w-full pl-8 pr-16',
          pathname === path && 'bg-accent'
        )}
      >
        <div
          className="relative max-h-5 flex-1 select-none overflow-hidden text-ellipsis break-all"
          title={thread.name}
        >
          <span className="whitespace-nowrap">{thread.name}</span>
        </div>
      </Link>
      {(isActive || pathname === path) && <div className="absolute right-2 top-2">{children}</div>}
    </div>
  ); 
}