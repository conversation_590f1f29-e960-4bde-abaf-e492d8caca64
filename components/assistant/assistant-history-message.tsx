// Inspired by Cha<PERSON><PERSON>-<PERSON><PERSON> and modified to fit the needs of this project
// @see https://github.com/mckaywrigley/chatbot-ui/blob/main/components/Chat/ChatMessage.tsx

import Image from "next/image";
import { useState, useRef } from "react";
import { Message as ThreadMessage } from 'openai/resources/beta/threads/messages';
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'

import { cn } from '@/lib/utils'
import { CodeBlock } from '@/components/ui/codeblock'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { BotAvatar } from "@/components/bot-avatar"
import { UserAvatar } from '@/components/user-avatar'
import { ChatMessageActions } from './history-message-actions'
import './index.css'

/*interface Message {
  id: string;
  role: "system" | "user" | "assistant" | "data";
  content: string;
  image?: string | null;
}*/

export interface AssistantHistoryMessageProps {
  isLoading?: boolean
  src?: string
  message: ThreadMessage
  chatId?: string
  path?: string
}

export function AssistantHistoryMessage({ 
  isLoading, 
  src, 
  message, 
  chatId,
  path, 
  ...props }: AssistantHistoryMessageProps) {
  const codeBlockRef = useRef<SyntaxHighlighter>(null);

  return (
    <div
      className={cn('group flex items-start gap-x-2 py-2 w-full',
      message.role === "user" && "justify-end")}
      {...props}
    >
      {message.role !== 'user' && src && <BotAvatar src={src} />}
      <div className="rounded-md px-4 py-2 max-w-lg text-sm break-words bg-primary/10">
        <div className="prose break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0 prose-a:text-pink-600 hover:prose-a:text-pink-500">
          {message.content[0].type === "text"
          ? message.content[0].text.value
              .split("\n")
              .map((text, index) => <p key={index} className="customize-box">{text}</p>)
          : null}
        </div>
      </div>
      {message.role === 'user' && <UserAvatar />}
      <ChatMessageActions message={message} chatId={chatId} path={path} />
    </div>
  )
}