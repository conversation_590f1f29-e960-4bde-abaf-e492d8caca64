// Inspired by Cha<PERSON><PERSON>-<PERSON><PERSON> and modified to fit the needs of this project
// @see https://github.com/mckaywrigley/chatbot-ui/blob/main/components/Chat/ChatMessage.tsx

import Image from "next/image";
import React, { JSX, useRef } from "react";
//import { Message } from '@ai-sdk/react';
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism'

import { cn } from '@/lib/utils'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { BotAvatar } from "@/components/bot-avatar"
import { UserAvatar } from '@/components/user-avatar'
import { ChatMessageActions } from '@/components/chat-message-actions'
import './index.css'

interface Message {
  id: string;
  role: "system" | "user" | "assistant";
  content: string;
  image?: string | null;
}

declare module 'ai' {
  interface Message {
    image?: string | null;
  }
}

export interface AssistantMessageProps {
  isLoading?: boolean
  src?: string
  message: Message
  chatId?: string
  path?: string
}

const roleToColorMap: Partial<Record<Message['role'], string>> = {
  system: 'red',
  user: 'black',
  assistant: 'green',
};

export function AssistantMessage({ 
  isLoading, 
  src, 
  message, 
  chatId, 
  path,
  ...props }: AssistantMessageProps) {
  const codeBlockRef = useRef<SyntaxHighlighter>(null);

  return (
    <div
      className={cn('group flex items-start gap-x-2 py-2 w-full',
      message.role === "user" && "justify-end")}
      {...props}
      style={{ color: roleToColorMap[message.role] }}
    >
      {message.role !== 'user' && src && <BotAvatar src={src} />}
      <div className="rounded-md px-4 py-2 max-w-lg text-sm break-words bg-primary/10">
        <MemoizedReactMarkdown
          className="prose break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0 prose-a:text-pink-600 hover:prose-a:text-pink-500"
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={[rehypeKatex]}
          components={{
            p({ children }) {
              const childArray = React.Children.toArray(children);
          
              const remainingContent = childArray.reduce<{
                images: React.ReactNode[];
                text: (string | JSX.Element)[];
              }>((acc, child) => {
                if (typeof child === 'string') {
                  acc.text.push(child);
                } else if (React.isValidElement(child)) {
                  if (child.type === 'img') {
                    acc.images.push(child);
                  } else if (child.type === 'a') {
                    acc.text.push(
                      <a href={(child as React.ReactElement<any>).props.href} key={acc.text.length}>
                        {(child as React.ReactElement<any>).props.children}
                      </a>
                    );
                  } else {
                    acc.text.push(child);
                  }
                }
                return acc;
              }, { images: [], text: [] });

              const images = childArray.filter(
                (child) =>
                  React.isValidElement(child) &&
                  child.type === "img" &&
                  typeof child !== "string" // Filter out string nodes
              );
              {console.log("childArray@images-map", images)}
              
              const renderedImages = images.map((image, index) => {
                if (typeof image === 'object' && image !== null && 'type' in image && typeof image.type === 'function') {
                  //@ts-ignore
                  const { src, alt } = image.props;
                  {console.log("childArray@image-src", src)}
                  return (
                    <div key={index}>
                      <Image
                        src={src as string}
                        alt={alt as string}
                        style={{
                          maxWidth: '160px',
                          maxHeight: '160px',
                        }}
                        className="
                        w-full h-auto
                        object-cover
                        cursor-pointer
                        hover:scale-110
                        transition
                        rounded-lg
                        border-1
                        m-0
                        "
                      />
                    </div>
                  );
                }
                return null;
              });
       
              return (
                <div>
                  {images.length > 0 && (
                    <div className={`grid grid-cols-${images.length === 2 ? 2 : images.length <= 1 ? 1 : 3} gap-2`}
                    >
                      {renderedImages}
                    </div>
                  )}
                  {/* Displaying text content */}
                  <p className="mt-1 mb-2 text-justify last:mb-0">{remainingContent.text}</p>
                </div>
              );
            },
            code(props) {
              const {children, className, node, ...rest} = props
              const match = /language-(\w+)/.exec(className || '')
              return match ? (
                <SyntaxHighlighter
                  {...rest}
                  ref={codeBlockRef}
                  PreTag="div"
                  children={String(children).replace(/\n$/, '')}
                  language={match[1]}
                  style={dark}
                />
              ) : (
                <code {...rest} className={className}>
                  {children}
                </code>
              );
            }
          }}
        >
          {message.content}
        </MemoizedReactMarkdown>
      </div>
      {message.role === 'user' && <UserAvatar />}
      <ChatMessageActions message={message} chatId={chatId} path={path} />
    </div>
  );
}