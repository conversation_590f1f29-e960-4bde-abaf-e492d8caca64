import { getUserThreads, renameUserThread, removeUserThread } from '@/app/actions/assistantActions'
import { SidebarActions } from './sidebar-actions'
import { SidebarItem } from './sidebar-item'
import { cache } from 'react'

export interface SidebarListProps {
  userId?: string
  companionId?: string
  path: string
}

const loadThreads = cache(async (userId?: string, companionId?: string) => {
  return await getUserThreads(userId, companionId)
})

export async function SidebarList({ userId, companionId, path }: SidebarListProps) {
  const threads = await loadThreads(userId, companionId)

  return (
    <div className="flex-1 overflow-auto">
      {threads?.length ? (
        <div className="space-y-1 px-2">
          {threads.map((thread, index) =>
            thread && (
              <div key={thread.id} className={index === 0 ? 'rounded-lg bg-muted/70' : ''}>
                <SidebarItem key={thread?.id} thread={thread} path={`${path}/${thread?.threadId!}`}>
                  <SidebarActions
                    thread={thread}
                    renameUserThread={renameUserThread}
                    removeUserThread={removeUserThread}
                    path={path}
                  />
                </SidebarItem>
              </div>
            )
          )}
        </div>
      ) : (
        <div className="p-8 text-center">
          <p className="text-sm text-muted-foreground">No chat history</p>
        </div>
      )}
    </div>
  )
}
