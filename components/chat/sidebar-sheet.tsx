'use client'

import * as React from 'react'


import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
//import { IconSidebar } from '@/components/ui/icons'
import { PiChatsDuotone } from "react-icons/pi";
import { defaultBackground } from '@/constants'

export interface SidebarProps {
  children?: React.ReactNode
}

export function SidebarSheet({ children }: SidebarProps) {
  const [isMounted, setIsMounted] = React.useState(false);
  const isLocalStorageAvailable = typeof localStorage !== 'undefined';
  const initialBackground = isLocalStorageAvailable ? localStorage.getItem('background') : defaultBackground;
  //const initialBackground = localStorage.getItem('background') || defaultBackground;
  const [background, setBackground] = React.useState(initialBackground || defaultBackground);
  
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  React.useEffect(() => {
    localStorage.setItem('background', background);
  }, [background]);
  
  if (!isMounted) {
    return null;
  }
  
  return (
    <Sheet modal={false} defaultOpen={false} >
      <SheetTrigger asChild>
        <Button variant="ghost" className="-ml-2 h-9 w-9 p-0">
          <PiChatsDuotone className="h-6 w-6" />
          <span className="sr-only">Toggle Sidebar</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" onInteractOutside={(e: any) => e.preventDefault()} 
        className="inset-y-0 flex h-auto w-[300px] flex-col rounded-r-lg border-0 p-0 bg-secondary/95"
        style={{ background }}
      >
        <SheetHeader className="p-4">
          <SheetTitle className="text-sm">Chat History</SheetTitle>
        </SheetHeader>
        {children}
      </SheetContent>
    </Sheet>
  )
}
