'use client'

import axios from 'axios'
import { DefaultChatTransport } from 'ai';
import { useChat, type UIMessage as Message} from '@ai-sdk/react';
import { 
  Companion, 
  Message as chatMessage, 
  Observations, 
  Todos,
  Persona } from '@prisma/client'
import { 
  Approaches,
  AskResponse,
  ChatRequest,
  SentimentData,
  PolarAreaChartType,
  QuestionWithOptions
} from '@/lib/post.types'

import { cn } from '@/lib/utils'
import { useUser } from '@clerk/nextjs'
import { useToast } from '@/components/ui/use-toast'
import { notFound, useRouter } from 'next/navigation'
import { ChatFixedHeader } from '@/components/chat-fixed-header'
import { ChatList } from '@/components/chat-list'
import { ChatPanel } from '@/components/chat-panel'
import { EmptyScreen } from '@/components/empty-screen'
import { ChatScrollAnchor } from '@/lib/hooks/chat-scroll-anchor'
import { useLocalStorage } from '@/lib/hooks/use-local-storage'
import { ObservationModal } from '@/components/modals/ObservationModal'
import ObservationNew from '@/components/observation/observation-new'
import PolarAreaChart from '@/components/post/PolarAreaChart'
import TodoList from '@/components/suggestion/TodoList'
import { updatePolarAreaChart } from '@/components/post/Sentiment'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { useState, useRef, useEffect } from 'react'
import { Button } from './ui/button'
import { Input } from './ui/input'

const IS_PREVIEW = process.env.VERCEL_ENV === 'preview'
export interface ChatProps extends React.ComponentProps<'div'> {
  initialMessages?: Message[]
  id?: string
  companion: Companion & {
    messages: chatMessage[]
    observations: Observations[]
    todos: Todos[]
    _count: {
      messages: number
    }
  }
  foundPersona : Persona | null
}

export function Chat({ id, initialMessages, companion, foundPersona, className }: ChatProps) {
  const observations: Observations[] = companion ? companion.observations : []
  const todos: Todos[] = companion ? companion.todos : []
  const router = useRouter()
  const { user } = useUser()
  const { toast } = useToast()
  const labels: string[] = [
    "Excited", "Delighted", "Happy", "Content", 
    "Relaxed", "Calm", "Tired", "Bored", "Depressed", 
    "Frustrated", "Angry", "Tense"]
  const backgroundColors = [
    'rgb(104, 159, 56, 0.9)',
    'rgb(255, 99, 132, 0.9)',
    'rgb(75, 192, 192, 0.9)',
    'rgb(255, 128, 171, 0.9)',
    'rgb(255, 205, 86, 0.9)',
    'rgb(54, 162, 235, 0.9)',
    'rgb(101, 31, 255, 0.9)',
    'rgb(158, 158, 158, 0.9)',
    'rgb(48, 79, 254, 0.9)',
    'rgb(0, 200, 83, 0.9)',
    'rgb(245, 0, 87, 0.9)',
    'rgb(244, 81, 30, 0.9)'
  ];
  const initialEmotionsData: PolarAreaChartType = {
    labels: labels,
    datasets: [
      {
        label: 'Emotions',
        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        fill: true,
        backgroundColor: backgroundColors,
        borderColor: 'rgb(132, 255, 255, 0.0)',
        pointBackgroundColor: 'rgb(132, 255, 255)',
        pointBorderColor: 'rgb(132, 255, 255, 0.0)',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(132, 255, 255, 0.2)'
      },
    ],
  }
  const [usePersona, setUsePersona] = useState<Persona | null>(foundPersona)
  const [useReflectionThreshold, setUseReflectionThreshold] = useState<number>(20)
  const [emotionsData, setEmotionsData] = useState<PolarAreaChartType>(initialEmotionsData)
  const [currentData, setCurrentData] = useState<SentimentData>({
    emotions: labels,
    stars: []
  })

  let chatRuns = 0

  const [previewToken, setPreviewToken] = useLocalStorage<string | null>(
    'ai-token',
    null
  )
  const [previewTokenDialog, setPreviewTokenDialog] = useState(IS_PREVIEW)
  const [previewTokenInput, setPreviewTokenInput] = useState(previewToken ?? '')
  const [input, setInput] = useState('');
  const { messages, sendMessage, regenerate, stop, status } = useChat({
    id,
    messages: initialMessages?.map((message) => ({
      id: message.id,
      parts: [{
        type: 'text',
        text: message.content,
      }],
      role: message.role,
    })),
    transport: new DefaultChatTransport({
      api: `/api/chat`,
      body: () => ({
        id,
        chatRuns,
        previewToken,
        useReflectionThreshold,
        persona: {
          name: usePersona?.name,
          age: usePersona?.age,
          traits: usePersona?.traits,
          status: usePersona?.status
        },
        companionInstruct: {
          companionId: companion.id,
          companionNickName: companion.name,
          companionInstructions: companion.instructions,
          companionSeed: companion.seed
        }
      })
    }),
    onFinish: async ({ message }) => {
      //if (chatRuns >= 0 ) {
      if (chatRuns % (useReflectionThreshold + 3) === 9) {
        try {
          const userMessages = messages.filter(
            message => message.role === 'user'
          )
          const sentiment: { data: SentimentData } = await axios.post(
            '/api/sentiment',
            {
              userMessages,
              useReflectionThreshold
            }
          )

          const sentimentResult = sentiment.data
          //console.log("sentimentResult: ", sentimentResult)

          const updateSentimentData = async () => {
            const newData: SentimentData = {
              emotions: labels.map(emotion => {
                const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(
                  emotion
                )
                return emotionIndex !== -1 ? emotion : emotion
              }),
              stars: labels.map(emotion => {
                const emotionIndex = (sentimentResult?.emotions ?? []).indexOf(
                  emotion
                )
                return emotionIndex !== -1
                  ? (sentimentResult?.stars ?? [])[emotionIndex]
                  : 0
              })
            }
            //console.log("newData", newData)

            const updateEmotions = () => {
              const newEmotionsData: PolarAreaChartType =
                updatePolarAreaChart(newData)
                setEmotionsData(newEmotionsData)
            }
            updateEmotions()
          }
          updateSentimentData()
        } catch (error) {
          toast({
            variant: 'destructive',
            description: 'Something went wrong.',
            duration: 3000
          })
          console.error('Error in sentiment analysis:', error)
        }
      }
      chatRuns = chatRuns + 1
      router.refresh()
    }
  })
  return (
    <>
      <div className="flex max-w-screen h-screen-[calc(100vh-3rem)] overflow-hidden">
        {/* Left Panel */}
        <div className="hidden h-[100vh] lg:block md:flex w-[45%] flex-grow pt-[0rem] px-1 overflow-x-overlay overflow-y-hidden">          
          <div className="flex flex-col h-[60%] justify-center overflow-y-hidden">
            <div className="flex items-center justify-start px-2 pt-2 pb-1 text-[16px] text-left text-secondary-foreground font-sans font-bold">
              <div className="pt-2">
                <ObservationModal companionId={companion.id} userId={user?.id!} path={`/chat/${companion.id}`} />
              </div>
              <div className="pt-2">
                My Observations
              </div>
            </div>
            <div className="mt-0">
              <ObservationNew observations={observations} />
            </div>
          </div>
          <div className="flex flex-col h-[35%] w-full px-2 justify-center items-center overflow-overlay">
            <PolarAreaChart data={emotionsData} />
          </div>
        </div>
        {/* Center Panel */}
        <div className="flex flex-col max-h-[100vh] h-auto w-full px-4 py-2 space-y-2 overflow-hidden">
          {companion ? <ChatFixedHeader companion={companion} countMessages={initialMessages?.length} /> : null}
          <div className="flex-grow pb-[150px] overflow-y-auto">
            {messages.length ? (
              <>
                <ChatList 
                  companion={companion}
                  isLoading={status != "ready" && status!= "error"}
                  messages={messages}
                />
                <ChatScrollAnchor trackVisibility={status != "ready" && status!= "error"} />
              </>
            ) : (
              <EmptyScreen setInput={setInput} />
            )}
          </div>
          <div className="w-full h-auto rounded-3xl flex-cols max-w-[100%] sticky bottom-0">
            <ChatPanel
              id={id}
              status={status}
              stop={stop}
              sendMessage={sendMessage}
              regenerate={regenerate}
              messages={messages}
              input={input}
              setInput={setInput}
            />

            <Dialog open={previewTokenDialog} onOpenChange={setPreviewTokenDialog}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Enter your OpenAI Key</DialogTitle>
                  <DialogDescription>
                    If you have not obtained your OpenAI API key, you can do so by{' '}
                    <a
                      href="https://platform.openai.com/signup/"
                      className="underline"
                    >
                      signing up
                    </a>{' '}
                    on the OpenAI website. This is only necessary for preview
                    environments so that the open source community can test the app.
                    The token will be saved to your browser&apos;s local storage under
                    the name <code className="font-mono">ai-token</code>.
                  </DialogDescription>
                </DialogHeader>
                <Input
                  value={previewTokenInput}
                  placeholder="OpenAI API key"
                  onChange={e => setPreviewTokenInput(e.target.value)}
                />
                <DialogFooter className="items-center">
                  <Button
                    onClick={() => {
                      setPreviewToken(previewTokenInput)
                      setPreviewTokenDialog(false)
                    }}
                  >
                    Save Token
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        {/* Right Panel */}
        <div className="hidden lg:block w-[45%] flex-grow pt-[2.75rem] px-1 overflow-hidden">
          <div className="flex flex-col pl-2 justify-center">
            <TodoList companionId={companion.id} todosData={todos ?? []}/>
          </div>
        </div>
      </div>
    </>
  )
}